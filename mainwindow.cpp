#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "camerastream.h"
#include "thumbnailprovider.h"
#include "CustomDialog.h"
#include <QDebug>
#include <QDateTime>
#include <QTimer>
#include <QDir>
#include <QFileInfo>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QDialog>
#include <QWidget>
#include <QPushButton>
#include <QScrollArea>
#include <QPixmap>
#include <QListView>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QFileIconProvider>
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>
#include <QKeyEvent>
#include <QEvent>
#include <QSet>
#include <unistd.h>
#include <uart.c>
#include <ch9350_mouse.h>


MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , isRecordEnabled(false)
    , isAudioEnabled(false)
    , isPhotoEnabled(false)
    , cameraStream(nullptr)
    , mainGroup(nullptr)
    , recordingIndicatorWidget(nullptr)
    , recordingDotLabel(nullptr)
    , recordingTimeLabel(nullptr)
    , blinkTimer(nullptr)
    , recordingTimer(nullptr)
    , isDotVisible(true)
    , m_player(nullptr)
{
    initializeUI();
    setupButtonGroups();
    setupRecordingIndicator();
    initializeCameraStream();

    // 初始化录像文件列表模型
    m_videoFileModel = new QStandardItemModel(this);

    // 初始化图片文件列表模型
    m_pictureFileModel = new QStandardItemModel(this);

    // 初始化页面历史记录
    pageHistory.clear();
    selectedButtonHistory.clear();

    // 程序启动时加载图片文件
    loadPictureFiles();

    system("export LIBINPUT_DEBUG_LEVEL=0");
    m_mouseThread = new ch9350_mouse("/dev/ttyS10", this);
    m_mouseThread->start();

}

void MainWindow::initializeUI()
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    showMaximized();

    // 初始状态下隐藏菜单
    ui->stackedWidget->hide();
    ui->help_menu->hide();
    ui->widget_2->hide();

}

void MainWindow::setupButtonGroups()
{
    // 创建主菜单按钮组 (页面0)
    mainGroup = new QButtonGroup(this);
    const QList<QAbstractButton*> mainButtons = {
        ui->Camera,      // 索引0 - 进入页面1
        ui->Recordset,   // 索引1 - 进入页面4
        ui->Filemanage,  // 索引2 - 进入页面6
        ui->Systemset,   // 索引3 - 进入页面9
    };

    // 添加主菜单按钮到组中
    for (int i = 0; i < mainButtons.size(); ++i) {
        mainGroup->addButton(mainButtons[i], i);
    }

    // 初始化子菜单组列表
    subGroups.clear();
    for (int i = 0; i < 10; ++i) { // 支持10个页面的子组
        subGroups.append(nullptr);
    }

    // 设置各页面的按钮组
    setupAllPageGroups();
}

void MainWindow::setupAllPageGroups()
{
    // 页面1: 摄像头选择页面 (Camera_UVC, Camera_HDMI)
    subGroups[1] = new QButtonGroup(this);
    subGroups[1]->addButton(ui->Camera_UVC, 0);
    subGroups[1]->addButton(ui->Camera_HDMI, 1);

    // 页面2: UVC设置页面
    subGroups[2] = new QButtonGroup(this);
    subGroups[2]->addButton(ui->UVC_brightness, 0);
    subGroups[2]->addButton(ui->UVC_contrast, 1);
    subGroups[2]->addButton(ui->UVC_hue, 2);
    subGroups[2]->addButton(ui->UVC_exposure_auto, 3);
    subGroups[2]->addButton(ui->UVC_exposure, 4);
    subGroups[2]->addButton(ui->UVC_white_auto, 5);
    subGroups[2]->addButton(ui->UVC_white, 6);

    // 页面3: HDMI设置页面
    subGroups[3] = new QButtonGroup(this);
    subGroups[3]->addButton(ui->HDMI_brightness, 0);
    subGroups[3]->addButton(ui->HDMI_contrast, 1);
    subGroups[3]->addButton(ui->HDMI_hue, 2);
    subGroups[3]->addButton(ui->HDMI_exposure_auto, 3);
    subGroups[3]->addButton(ui->HDMI_exposure, 4);
    subGroups[3]->addButton(ui->HDMI_white_auto, 5);
    subGroups[3]->addButton(ui->HDMI_white, 6);

    // 页面4: 录像设置页面 (record_UVC, record_HDMI)
    subGroups[4] = new QButtonGroup(this);
    subGroups[4]->addButton(ui->record_UVC, 0);
    subGroups[4]->addButton(ui->record_HDMI, 1);

    // 页面6: 文件管理页面 (file_picture, file_video)
    subGroups[6] = new QButtonGroup(this);
    subGroups[6]->addButton(ui->file_picture, 0);
    subGroups[6]->addButton(ui->file_video, 1);

    
}



void MainWindow::initializeCameraStream()
{
    cameraStream = new CameraStream(this);
    if (cameraStream) {
        // 循环检测设备，直到找到支持4K60的设备
        bool deviceReady = false;

        while (!deviceReady)
        {
            // 检测设备是否存在
            if (cameraStream->checkVideoDevice()) {
                qDebug() << "摄像头设备已找到，开始启动摄像头";
                deviceReady = true;
            }
            if (!deviceReady) {
                sleep(1); // 等待1秒
            }
        }

        if (deviceReady) {
            // 设备检测通过，启动摄像头
            if (cameraStream->start_camera()) {

                // 连接摄像头断开和重连信号
                connect(cameraStream, &CameraStream::cameraDisconnected, this, &MainWindow::onCameraDisconnected);
                connect(cameraStream, &CameraStream::cameraReconnected, this, &MainWindow::onCameraReconnected);
                // 连接拍照成功信号
                connect(cameraStream, &CameraStream::photoTaken, this, &MainWindow::onPhotoTaken);
                // 等待摄像头初始化完成
                usleep(1000*500); // 500ms
            } else {
                qWarning() << "摄像头启动失败";
            }
        } else {
            qWarning() << "设备检测失败，无法找到支持4K60的video11设备";
        }
    }
}

MainWindow::~MainWindow()
{
    if (cameraStream) {
        delete cameraStream;
    }

    // 清理定时器
    if (blinkTimer) {
        blinkTimer->stop();
        delete blinkTimer;
    }
    if (recordingTimer) {
        recordingTimer->stop();
        delete recordingTimer;
    }
    if (m_mouseThread) {
        m_mouseThread->requestInterruption();
        m_mouseThread->wait();
        delete m_mouseThread;
    }

    // 清理播放器
    if (m_player) {
        delete m_player;
    }

    delete ui;
}

void MainWindow::keyPressEvent(QKeyEvent *e)
{
    // 检查Ctrl+R组合键，用于强制重启摄像头
    if (e->modifiers() & Qt::ControlModifier && e->key() == Qt::Key_R) {
        qDebug() << "检测到Ctrl+R快捷键，强制重启摄像头";
        forceRestartCamera();
        return;
    }

    switch (e->key()) {
        case Qt::Key_Q:
            handleQKeyPress();
            break;
        case Qt::Key_Return:
        case Qt::Key_Enter:
            handleEnterKeyPress();
            break;
        case Qt::Key_Left:
        case Qt::Key_Right:
        case Qt::Key_Up:
        case Qt::Key_Down:
            handleArrowKeyPress(e->key());
            break;
        case Qt::Key_R:
            handleRKeyPress();
            break;
        case Qt::Key_P:
            handlePKeyPress();
            break;
        case Qt::Key_X:
            handleXKeyPress();

        default:
            QMainWindow::keyPressEvent(e);
            break;
    }
}

void MainWindow::handleQKeyPress()
{
    int currentPage = ui->stackedWidget->currentIndex();

    if (currentPage != 0) {
        // 从子页面返回上一页面
        returnToPreviousPage();
    } else {
        // 在主菜单页面，切换主菜单显示/隐藏
        toggleMainMenuVisibility();
    }
}

void MainWindow::handleRKeyPress()
{
    // R键处理录像功能
    qDebug() << "R key pressed - toggle recording";

    // 验证摄像头流
    if (!validateCameraStream()) {
        qWarning() << "Cannot start/stop recording: camera stream not available";
        return;
    }

    // 切换录像状态
    toggleRecordingState();
}

void MainWindow::handlePKeyPress()
{
    // P键处理拍照功能
    qDebug() << "P key pressed - take photo";

    // 验证摄像头流
    if (!validateCameraStream()) {
        qWarning() << "Cannot take photo: camera stream not available";
        return;
    }

    // 执行拍照
    togglePhotoState();
}

void MainWindow::handleXKeyPress()
{
    if (ui->widget_2->isVisible()) {
        ui->widget_2->hide();
    }
    else
    {
        ui->widget_2->show();
    }
}

void MainWindow::handleEnterKeyPress()
{
    int currentPage = ui->stackedWidget->currentIndex();

    switch (currentPage) {
        case 0: // 主菜单页面
            handleMainMenuEnter();
            break;
        case 1: // 摄像头选择页面
            handleCameraMenuEnter();
            break;
        case 4: // 录像设置页面
            handleRecordMenuEnter();
            break;
        case 6: // 文件管理页面
            handleFileMenuEnter();
            break;
        case 7: // 图片文件页面
            viewSelectedPicture(); // 查看选中的图片
            break;
        case 8: // 视频文件页面
            playSelectedVideo();
            break;
        default:
            // 其他页面不处理Enter键
            break;
    }
}

void MainWindow::handleMainMenuEnter()
{
    QAbstractButton *current = mainGroup->checkedButton();
    if (!current) return;

    int buttonId = mainGroup->id(current);
    switch (buttonId) {
        case 0: // Camera
            navigateToPage(1, 0); // 进入页面1，选中第一个按钮
            break;
        case 1: // Recordset
            navigateToPage(4, 0); // 进入页面4，选中第一个按钮
            break;
        case 2: // Filemanage
            navigateToPage(6, 0); // 进入页面6，选中第一个按钮
            break;
        case 3: // Systemset
            navigateToPage(9, 0); // 进入页面9
            break;
    }
}

void MainWindow::handleCameraMenuEnter()
{
    if (!subGroups[1]) return;

    QAbstractButton *current = subGroups[1]->checkedButton();
    if (!current) return;

    int buttonId = subGroups[1]->id(current);
    switch (buttonId) {
        case 0: // Camera_UVC
            navigateToPage(2, 0); // 进入页面2
            break;
        case 1: // Camera_HDMI
            navigateToPage(3, 0); // 进入页面3
            break;
    }
}

void MainWindow::handleRecordMenuEnter()
{
    if (!subGroups[4]) return;

    QAbstractButton *current = subGroups[4]->checkedButton();
    if (!current) return;

    // 无论选择哪个选项，都进入页面5
    navigateToPage(5, 0);
}

void MainWindow::handleFileMenuEnter()
{
    if (!subGroups[6]) return;

    QAbstractButton *current = subGroups[6]->checkedButton();
    if (!current) return;

    int buttonId = subGroups[6]->id(current);
    switch (buttonId) {
        case 0: // file_picture

            navigateToPage(7, 0); // 进入页面7
            loadPictureFiles(false); // 加载图片文件

            break;
        case 1: // file_video

            navigateToPage(8, 0); // 进入页面8
            loadVideoFiles(); // 加载视频文件
            break;
    }
}

void MainWindow::handleArrowKeyPress(int key)
{
    int currentPage = ui->stackedWidget->currentIndex();

    if (currentPage == 7) {
        // 在图片查看页面，确保 ListView 有焦点
        if (!ui->listView_viewpictures->hasFocus()) {
            ui->listView_viewpictures->setFocus();
        }

        // 检查是否有文件
        if (m_pictureFileModel->rowCount() == 0) {
            return; // 没有文件，不处理导航
        }

        QModelIndex currentIndex = ui->listView_viewpictures->currentIndex();
        if (!currentIndex.isValid()) {
            // 如果没有选中项，选中第一个
            QModelIndex firstIndex = m_pictureFileModel->index(0, 0);
            ui->listView_viewpictures->setCurrentIndex(firstIndex);
            ui->listView_viewpictures->selectionModel()->select(firstIndex, QItemSelectionModel::ClearAndSelect);
            return;
        }

        // 计算新的索引
        QModelIndex newIndex;
        int currentRow = currentIndex.row();
        int totalRows = m_pictureFileModel->rowCount();

        if (key == Qt::Key_Up || key == Qt::Key_Left) {
            // 向上/向左移动
            int newRow = (currentRow - 1 + totalRows) % totalRows;
            newIndex = m_pictureFileModel->index(newRow, 0);
        } else if (key == Qt::Key_Down || key == Qt::Key_Right) {
            // 向下/向右移动
            int newRow = (currentRow + 1) % totalRows;
            newIndex = m_pictureFileModel->index(newRow, 0);
        }

        // 只有在新索引有效时才更新选择
        if (newIndex.isValid()) {
            ui->listView_viewpictures->setCurrentIndex(newIndex);
            ui->listView_viewpictures->selectionModel()->select(newIndex, QItemSelectionModel::ClearAndSelect);
        }

    } else if (currentPage == 8) {
        // 在视频文件页面，确保 ListView 有焦点
        if (!ui->listView_videofile->hasFocus()) {
            ui->listView_videofile->setFocus();
        }

        // 检查是否有文件
        if (m_videoFileModel->rowCount() == 0) {
            return; // 没有文件，不处理导航
        }

        QModelIndex currentIndex = ui->listView_videofile->currentIndex();
        if (!currentIndex.isValid()) {
            // 如果没有选中项，选中第一个
            QModelIndex firstIndex = m_videoFileModel->index(0, 0);
            ui->listView_videofile->setCurrentIndex(firstIndex);
            ui->listView_videofile->selectionModel()->select(firstIndex, QItemSelectionModel::ClearAndSelect);
            return;
        }

        // 手动处理方向键导航，确保边界检查
        int currentRow = currentIndex.row();
        int totalRows = m_videoFileModel->rowCount();

        // 计算每行的列数（基于网格大小和ListView宽度）
        int listWidth = ui->listView_videofile->width();
        int itemWidth = ui->listView_videofile->gridSize().width();
        int columnsPerRow = qMax(1, listWidth / itemWidth);

        QModelIndex newIndex;

        switch (key) {
            case Qt::Key_Left:
                // 左移：如果不是第一个文件，则移动到前一个
                if (currentRow > 0) {
                    newIndex = m_videoFileModel->index(currentRow - 1, 0);
                }
                break;

            case Qt::Key_Right:
                // 右移：如果不是最后一个文件，则移动到后一个
                if (currentRow < totalRows - 1) {
                    newIndex = m_videoFileModel->index(currentRow + 1, 0);
                }
                break;

            case Qt::Key_Up:
                // 上移：如果当前行不是第一行，则移动到上一行的对应位置
                if (currentRow >= columnsPerRow) {
                    newIndex = m_videoFileModel->index(currentRow - columnsPerRow, 0);
                }
                break;

            case Qt::Key_Down:
                // 下移：如果下一行存在对应位置，则移动
                if (currentRow + columnsPerRow < totalRows) {
                    newIndex = m_videoFileModel->index(currentRow + columnsPerRow, 0);
                }
                break;
        }

        // 只有在新索引有效时才更新选择
        if (newIndex.isValid()) {
            ui->listView_videofile->setCurrentIndex(newIndex);
            ui->listView_videofile->selectionModel()->select(newIndex, QItemSelectionModel::ClearAndSelect);
        }

    } else {
        // 处理有按钮组的页面导航
        handlePageNavigation(currentPage, key);
    }
}

void MainWindow::handlePageNavigation(int page, int key)
{
    QButtonGroup *currentGroup = nullptr;

    // 确定当前页面使用的按钮组
    if (page == 0) {
        currentGroup = mainGroup;
    } else if (page == 1 || page == 2 || page == 3 || page == 4 || page == 6) {
        // 页面1: 摄像头选择页面
        // 页面2: UVC设置页面
        // 页面3: HDMI设置页面
        // 页面4: 录像设置页面
        // 页面6: 文件管理页面
        currentGroup = subGroups[page];
    }

    if (!currentGroup) {
        qDebug() << "页面" << page << "的按钮组不存在，无法处理导航";
        return;
    }

    // 处理上下键导航
    if (key == Qt::Key_Up || key == Qt::Key_Down) {
        QAbstractButton *current = currentGroup->checkedButton();
        if (!current) {
            // 如果没有选中的按钮，选中第一个按钮
            if (currentGroup->buttons().size() > 0) {
                currentGroup->button(0)->setChecked(true);
                qDebug() << "页面" << page << "没有选中按钮，已自动选中第一个按钮";
            }
            return;
        }

        int currentId = currentGroup->id(current);
        int buttonCount = currentGroup->buttons().size();
        int nextId = (key == Qt::Key_Up) ?
            (currentId - 1 + buttonCount) % buttonCount :
            (currentId + 1) % buttonCount;

        currentGroup->button(nextId)->setChecked(true);
        qDebug() << "页面" << page << "导航从按钮" << currentId << "到按钮" << nextId;
    }
    // 对于某些页面，左右键可能有特殊功能
    else if (key == Qt::Key_Left || key == Qt::Key_Right) {
        // 在设置页面，左右键可以用于调整参数值
        if (page == 2 || page == 3) {
            // UVC/HDMI设置页面的参数调整
            // 这里可以添加参数调整逻辑
        }
    }
}

void MainWindow::navigateToMainMenu()
{
    ui->stackedWidget->setCurrentIndex(0);
    ui->Camera->setChecked(true);
}

void MainWindow::toggleMainMenuVisibility()
{
    ui->stackedWidget->setCurrentIndex(0);
    if (ui->stackedWidget->isHidden()) {
        ui->stackedWidget->show();
        ui->help_menu->show();
    } else {
        ui->stackedWidget->hide();
        ui->help_menu->hide();
    }
    ui->Camera->setChecked(true);
}



void MainWindow::navigateMainMenu(bool moveUp)
{
    QAbstractButton *current = mainGroup->checkedButton();
    if (!current) return;

    int currentId = mainGroup->id(current);
    int buttonCount = mainGroup->buttons().size();
    int nextId = moveUp ?
        (currentId - 1 + buttonCount) % buttonCount :
        (currentId + 1) % buttonCount;

    mainGroup->button(nextId)->setChecked(true);
}

void MainWindow::navigateToPage(int pageIndex, int buttonIndex)
{
    // 记录当前页面和选中的按钮到历史记录
    int currentPage = ui->stackedWidget->currentIndex();
    pageHistory.append(currentPage);

    // 记录当前选中的按钮索引
    int currentButtonIndex = 0;
    if (currentPage == 0 && mainGroup->checkedButton()) {
        currentButtonIndex = mainGroup->id(mainGroup->checkedButton());
    } else if (subGroups[currentPage] && subGroups[currentPage]->checkedButton()) {
        currentButtonIndex = subGroups[currentPage]->id(subGroups[currentPage]->checkedButton());
    }
    selectedButtonHistory.append(currentButtonIndex);

    // 切换到新页面
    ui->stackedWidget->setCurrentIndex(pageIndex);

    // 设置新页面的默认选中按钮
    if (pageIndex == 0 && mainGroup) {
        // 主菜单页面
        if (buttonIndex < mainGroup->buttons().size()) {
            mainGroup->button(buttonIndex)->setChecked(true);
        }
    } else if (pageIndex < subGroups.size() && subGroups[pageIndex]) {
        // 子菜单页面
        if (buttonIndex < subGroups[pageIndex]->buttons().size()) {
            subGroups[pageIndex]->button(buttonIndex)->setChecked(true);
            qDebug() << "页面" << pageIndex << "已选中按钮" << buttonIndex;
        } else {
            qDebug() << "页面" << pageIndex << "按钮索引" << buttonIndex << "超出范围，按钮总数:" << subGroups[pageIndex]->buttons().size();
        }
    } else {
        qDebug() << "页面" << pageIndex << "的按钮组不存在或未初始化";
    }
}

void MainWindow::returnToPreviousPage()
{
    // 获取当前页面，如果是文件列表页面，清除焦点
    int currentPage = ui->stackedWidget->currentIndex();
    if (currentPage == 7) {
        // 从图片文件页面返回，清除ListView焦点
        ui->listView_viewpictures->clearFocus();
    } else if (currentPage == 8) {
        // 从视频文件页面返回，清除ListView焦点
        ui->listView_videofile->clearFocus();
    }

    if (pageHistory.isEmpty() || selectedButtonHistory.isEmpty()) {
        // 如果没有历史记录，返回主菜单
        navigateToMainMenu();
        return;
    }

    // 获取上一页面信息
    int previousPage = pageHistory.takeLast();
    int previousButtonIndex = selectedButtonHistory.takeLast();

    // 切换到上一页面
    ui->stackedWidget->setCurrentIndex(previousPage);

    // 恢复上一页面的选中按钮
    if (previousPage == 0 && mainGroup) {
        // 主菜单页面
        if (previousButtonIndex < mainGroup->buttons().size()) {
            mainGroup->button(previousButtonIndex)->setChecked(true);
        }
    } else if (subGroups[previousPage]) {
        // 子菜单页面
        if (previousButtonIndex < subGroups[previousPage]->buttons().size()) {
            subGroups[previousPage]->button(previousButtonIndex)->setChecked(true);
        }
    }
}

void MainWindow::toggleRecordingState()
{
    isRecordEnabled = !isRecordEnabled;
    handleRecordingStateChange();
}



void MainWindow::toggleAudioState()
{
    if (!validateCameraStream()) {
        return;
    }

    // 检查是否正在录像，如果是则不允许修改音频状态
    if (cameraStream->isRecording) {
        qDebug() << "Cannot change audio state while recording";
        return;
    }

    // 切换音频状态
    curaudiostate = !curaudiostate;

    // 同时更新全局音频状态（为了兼容性）
    isAudioEnabled = curaudiostate;

    qDebug() << "Audio state changed to:" << curaudiostate;
}

void MainWindow::togglePhotoState()
{
    if (!validateCameraStream()) {
        return;
    }



    // 拍照功能，直接调用拍照
    cameraStream->takePhoto();
    qDebug() << "Photo request submitted";
}



void MainWindow::handleRecordingStateChange()
{
    if (!validateCameraStream()) {
        return;
    }

    if (isRecordEnabled) {
        startRecording();
    } else {
        stopRecording();
    }
}



bool MainWindow::validateCameraStream() const
{
    if (!cameraStream) {
        qWarning() << "CameraStream instance does not exist";
        return false;
    }
    return true;
}

bool MainWindow::isValidChannel(int channel) const
{
    return channel >= 0 && channel < 4;
}

void MainWindow::startRecording()
{
    qDebug() << "Starting recording...";
    cameraStream->startRecording();

    // 显示录像指示器
    showRecordingIndicator();
}

void MainWindow::stopRecording()
{
    qDebug() << "Stopping recording...";
    cameraStream->stopRecording();

    // 隐藏录像指示器
    hideRecordingIndicator();
}

void MainWindow::disableCameraControls()
{
    // 录像时禁用音频控制，防止用户修改音频状态
    qDebug() << "Camera controls disabled during recording";
}

void MainWindow::enableCameraControls()
{
    // 停止录像时重新启用音频控制
    qDebug() << "Camera controls enabled after recording stopped";
}

void MainWindow::setupRecordingIndicator()
{
    // 获取UI中的录像指示器组件
    recordingIndicatorWidget = ui->recordingIndicatorWidget;
    recordingDotLabel = ui->recordingDotLabel;
    recordingTimeLabel = ui->recordingTimeLabel;

    // 初始状态下隐藏录像指示器
    recordingIndicatorWidget->hide();

    // 设置录像指示器的Z-order，确保它在最上层
    recordingIndicatorWidget->raise();

    // 初始定位到右上角
    positionRecordingIndicator();

    // 创建闪烁定时器
    blinkTimer = new QTimer(this);
    connect(blinkTimer, &QTimer::timeout, this, &MainWindow::toggleRecordingDot);

    // 创建录像时间计时器
    recordingTimer = new QTimer(this);
    connect(recordingTimer, &QTimer::timeout, this, &MainWindow::updateRecordingTime);
}

void MainWindow::showRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 显示录像指示器
    recordingIndicatorWidget->show();

    // 记录录像开始时间
    recordingStartTime = QDateTime::currentDateTime();

    // 重置时间显示
    recordingTimeLabel->setText("00:00:00");

    // 启动闪烁定时器（每500ms闪烁一次）
    isDotVisible = true;
    
    recordingDotLabel->setStyleSheet("background-color: red; border-radius: 10px;");
    blinkTimer->start(500);

    // 启动录像时间计时器（每秒更新一次）
    recordingTimer->start(1000);

    qDebug() << "Recording indicator shown";
}

void MainWindow::hideRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 停止定时器
    if (blinkTimer) {
        blinkTimer->stop();
    }
    if (recordingTimer) {
        recordingTimer->stop();
    }

    // 隐藏录像指示器
    recordingIndicatorWidget->hide();

    qDebug() << "Recording indicator hidden";
}

void MainWindow::updateRecordingTime()
{
    if (!recordingTimeLabel) return;

    // 计算录像时长
    qint64 elapsedSeconds = recordingStartTime.secsTo(QDateTime::currentDateTime());

    // 转换为时:分:秒格式
    int hours = elapsedSeconds / 3600;
    int minutes = (elapsedSeconds % 3600) / 60;
    int seconds = elapsedSeconds % 60;

    QString timeText = QString("%1:%2:%3")
                       .arg(hours, 2, 10, QChar('0'))
                       .arg(minutes, 2, 10, QChar('0'))
                       .arg(seconds, 2, 10, QChar('0'));

    recordingTimeLabel->setText(timeText);
}

void MainWindow::toggleRecordingDot()
{
    if (!recordingDotLabel) return;

    
    isDotVisible = !isDotVisible;

   
    if (isDotVisible) {
        recordingDotLabel->setStyleSheet("background-color: red; border-radius: 10px;");
    } else {
        recordingDotLabel->setStyleSheet("background-color: transparent; border-radius: 10px;");
    }
}

void MainWindow::positionRecordingIndicator()
{
    if (!recordingIndicatorWidget) return;

    // 获取主窗口的大小
    QSize windowSize = this->size();

    // 计算右上角位置
    int x = windowSize.width() - recordingIndicatorWidget->width() - 20;  // 距离右边20像素
    int y = 20;  // 距离顶部20像素

    // 设置位置
    recordingIndicatorWidget->move(x, y);

    qDebug() << "Recording indicator positioned at:" << x << y;
}


void MainWindow::onCameraDisconnected()
{
    qWarning() << "摄像头已断开连接";

    // 如果正在录像，强制停止
    if (cameraStream && cameraStream->isRecording) {
        qWarning() << "摄像头断开，强制停止录像";
        isRecordEnabled = false;
        hideRecordingIndicator();
    }
}

void MainWindow::onCameraReconnected()
{
    qDebug() << "摄像头已重新连接";

}

void MainWindow::onPhotoTaken()
{
    loadPictureFiles(false);
}

void MainWindow::forceRestartCamera()
{

    if (cameraStream) {
        cameraStream->forceRestartCamera();
    } else {
        qWarning() << "摄像头流对象不存在，无法重启";
    }
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 窗口大小改变时重新定位录像指示器
    positionRecordingIndicator();
}

void MainWindow::loadVideoFiles()
{
    // 首先清理无效的缩略图文件
    cleanupInvalidThumbnails();

    // 清空模型
    m_videoFileModel->clear();

    // 设置 ListView 的模型和属性
    ui->listView_videofile->setModel(m_videoFileModel);
    ui->listView_videofile->setViewMode(QListView::IconMode);
    ui->listView_videofile->setIconSize(QSize(128, 128));
    ui->listView_videofile->setGridSize(QSize(160, 190)); // 增加高度以容纳多行文本
    ui->listView_videofile->setResizeMode(QListView::Adjust);
    ui->listView_videofile->setMovement(QListView::Static);
    ui->listView_videofile->setWrapping(true);
    ui->listView_videofile->setSpacing(8);
    ui->listView_videofile->setWordWrap(true); // 启用文字换行

    // 安装事件过滤器，确保Q键事件能传递到主窗口
    ui->listView_videofile->installEventFilter(this);

    // 设置焦点策略，避免蓝色焦点框
    ui->listView_videofile->setFocusPolicy(Qt::StrongFocus);

    // 设置 ListView 样式
    ui->listView_videofile->setStyleSheet(
        "QListView {"
        "   background-color: rgba(119, 118, 123, 127);"
        "   border: none;"
        "   outline: none;"
        "   show-decoration-selected: 0;"
        "}"
        "QListView::item {"
        "   background-color: rgba(50, 50, 50, 150);"
        "   border: 1px solid rgba(100, 100, 100, 100);"
        "   border-radius: 8px;"
        "   margin: 5px;"
        "   padding: 8px;"
        "   text-align: center;"
        "   outline: none;"
        "}"
        "QListView::item:hover {"
        "   background-color: rgba(70, 70, 70, 180);"
        "   border: 1px solid rgba(150, 150, 150, 150);"
        "   outline: none;"
        "}"
        "QListView::item:selected {"
        "   background-color: rgba(100, 150, 200, 180);"
        "   border: 2px solid rgba(150, 200, 255, 200);"
        "   outline: none;"
        "}"
        "QListView::item:focus {"
        "   outline: none;"
        "   border: 2px solid rgba(150, 200, 255, 200);"
        "}"
    );

    // 创建缩略图提供器
    ThumbnailProvider thumbnailProvider;

    // 扫描录像目录
    QString recordingDir = "/data/recordings";
    QDir dir(recordingDir);

    if (!dir.exists()) {
        // 如果目录不存在，添加提示项
        QStandardItem* item = new QStandardItem("录像目录不存在或为空");
        item->setFlags(Qt::ItemIsEnabled); // 不可选择
        item->setTextAlignment(Qt::AlignCenter);
        m_videoFileModel->appendRow(item);
        return;
    }

    // 获取所有视频文件，按修改时间排序（最新的在前）
    QStringList filters;
    filters << "*.mp4" << "*.avi" << "*.mkv";
    QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files, QDir::Time);

    if (fileList.isEmpty()) {
        // 如果没有视频文件，添加提示项
        QStandardItem* item = new QStandardItem("暂无录像文件");
        item->setFlags(Qt::ItemIsEnabled); // 不可选择
        item->setTextAlignment(Qt::AlignCenter);
        m_videoFileModel->appendRow(item);
        return;
    }

    // 为每个视频文件创建列表项
    for (const QFileInfo& fileInfo : fileList) {
        // 获取缩略图
        QIcon icon = thumbnailProvider.icon(fileInfo);

        // 创建文件名（去掉扩展名）
        QString baseName = fileInfo.completeBaseName();

        // 处理长文件名，智能换行
        QString displayName = baseName;
        if (baseName.length() > 15) {
            // 如果文件名太长，在合适的位置插入换行符
            // 优先在下划线、连字符或数字后换行
            QRegExp breakPoints("[_-]|(?<=\\d)(?=\\D)|(?<=\\D)(?=\\d)");
            QStringList parts;
            int lastIndex = 0;
            int index = 0;

            while ((index = breakPoints.indexIn(baseName, lastIndex)) != -1) {
                if (index - lastIndex > 8) { // 如果这一段够长了
                    parts.append(baseName.mid(lastIndex, index - lastIndex + 1));
                    lastIndex = index + 1;
                }
            }

            // 添加剩余部分
            if (lastIndex < baseName.length()) {
                parts.append(baseName.mid(lastIndex));
            }

            // 如果没有找到合适的断点，按固定长度分割
            if (parts.isEmpty() || parts.size() == 1) {
                parts.clear();
                for (int i = 0; i < baseName.length(); i += 12) {
                    parts.append(baseName.mid(i, 12));
                }
            }

            displayName = parts.join("\n");
        }

        // 创建列表项 - 显示处理后的文件名
        QStandardItem* item = new QStandardItem(icon, displayName);
        item->setFlags(Qt::ItemIsEnabled | Qt::ItemIsSelectable);
        item->setData(fileInfo.absoluteFilePath(), Qt::UserRole); // 存储完整路径
        item->setTextAlignment(Qt::AlignCenter);

        // 设置工具提示（详细信息在工具提示中显示）
        QString sizeStr = QString::number(fileInfo.size() / (1024.0 * 1024.0), 'f', 1) + " MB";
        QString timeStr = fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss");
        item->setToolTip(QString("文件: %1\n大小: %2\n修改时间: %3")
                        .arg(fileInfo.fileName(), sizeStr, timeStr));

        m_videoFileModel->appendRow(item);
    }

    // 默认选中第一个文件并设置焦点
    if (m_videoFileModel->rowCount() > 0) {
        QModelIndex firstIndex = m_videoFileModel->index(0, 0);
        ui->listView_videofile->setCurrentIndex(firstIndex);
        ui->listView_videofile->selectionModel()->select(firstIndex, QItemSelectionModel::ClearAndSelect);

        // 确保 ListView 获得焦点，这样键盘导航才能工作
        ui->listView_videofile->setFocus();

        qDebug() << "已默认选中第一个录像文件并设置焦点";
    }

    qDebug() << "已加载" << fileList.size() << "个录像文件到 ListView";
}

void MainWindow::cleanupInvalidThumbnails()
{
    qDebug() << "开始清理无效的缩略图文件...";

    QString recordingDir = "/data/recordings";
    QString thumbDir = "/data/thumb";

    // 检查目录是否存在
    QDir recordingsFolder(recordingDir);
    QDir thumbFolder(thumbDir);

    if (!recordingsFolder.exists()) {
        qWarning() << "录像目录不存在:" << recordingDir;
        return;
    }

    if (!thumbFolder.exists()) {
        qDebug() << "缩略图目录不存在:" << thumbDir << "，无需清理";
        return;
    }

    // 获取所有录像文件（去掉扩展名）
    QStringList videoFilters;
    videoFilters << "*.mp4" << "*.avi" << "*.mkv";
    QFileInfoList videoFiles = recordingsFolder.entryInfoList(videoFilters, QDir::Files);

    QSet<QString> videoBasenames;
    for (const QFileInfo& videoFile : videoFiles) {
        // 获取不带扩展名的文件名
        QString basename = videoFile.completeBaseName();
        videoBasenames.insert(basename);
    }

    qDebug() << "找到" << videoBasenames.size() << "个录像文件";

    // 获取所有缩略图文件（包括隐藏文件）
    QStringList thumbFilters;
    thumbFilters << ".thumb_*";
    QFileInfoList thumbFiles = thumbFolder.entryInfoList(thumbFilters, QDir::Files | QDir::Hidden);

    qDebug() << "找到" << thumbFiles.size() << "个缩略图文件";

    int deletedCount = 0;

    // 检查每个缩略图文件
    for (const QFileInfo& thumbFile : thumbFiles) {
        QString thumbFileName = thumbFile.fileName();

        // 缩略图文件名格式：.thumb_录像文件名.jpg
        // 提取录像文件名部分
        QString videoBasename;

        if (thumbFileName.startsWith(".thumb_")) {
            // 去掉 ".thumb_" 前缀
            videoBasename = thumbFileName.mid(7); // 7 = ".thumb_".length()

            // 去掉 ".jpg" 后缀（如果有）
            if (videoBasename.endsWith(".jpg")) {
                videoBasename = videoBasename.left(videoBasename.length() - 4);
            }
        } else {
            qWarning() << "缩略图文件名格式不正确:" << thumbFileName;
            continue;
        }

        // 检查对应的录像文件是否存在
        if (!videoBasenames.contains(videoBasename)) {
            // 录像文件不存在，删除缩略图
            QString thumbFilePath = thumbFile.absoluteFilePath();

            if (QFile::remove(thumbFilePath)) {
                qDebug() << "已删除无效缩略图:" << thumbFileName << "(对应录像文件:" << videoBasename << ")";
                deletedCount++;
            } else {
                qWarning() << "无法删除缩略图文件:" << thumbFilePath;
            }
        }
    }

    qDebug() << "缩略图清理完成，共删除" << deletedCount << "个无效缩略图文件";
}

void MainWindow::playSelectedVideo()
{
    // 获取当前选中的项
    QModelIndex currentIndex = ui->listView_videofile->currentIndex();
    if (!currentIndex.isValid()) {
        qWarning() << "没有选中的录像文件";
        return;
    }

    // 获取文件路径
    QString filePath = currentIndex.data(Qt::UserRole).toString();
    if (filePath.isEmpty()) {
        qWarning() << "无法获取录像文件路径";
        return;
    }

    qDebug() << "准备播放录像文件:" << filePath;

    // 如果已有播放器实例，先清理
    if (m_player) {
        delete m_player;
        m_player = nullptr;
    }

    // 创建播放器实例并播放视频（不设置父窗口，避免阻塞）
    m_player = new player(nullptr, filePath);

    // 设置播放器窗口属性，确保不阻塞主窗口
    m_player->setAttribute(Qt::WA_DeleteOnClose, true);

    // 连接播放器关闭信号，确保正确清理并恢复焦点
    connect(m_player, &QWidget::destroyed, this, [this]() {
        m_player = nullptr;
        qDebug() << "播放器已关闭并清理";

        // 播放器关闭后，重新激活主窗口并获取焦点
        this->activateWindow();
        this->raise();
        this->setFocus();

        // 确保录像文件列表视图也获得焦点
        if (ui->stackedWidget->currentIndex() == 8) {
            ui->listView_videofile->setFocus();
        }

        qDebug() << "主窗口焦点已恢复";
    });

    // 显示播放器窗口
    m_player->show();

    qDebug() << "播放器已启动";
}

bool MainWindow::eventFilter(QObject *obj, QEvent *event)
{
    // 如果是ListView的按键事件
    if ((obj == ui->listView_videofile || obj == ui->listView_viewpictures) && event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        // 如果是Q键，直接传递给主窗口处理
        if (keyEvent->key() == Qt::Key_Q) {
            handleQKeyPress();
            return true; // 事件已处理，不再传递
        }

        // 其他按键让ListView正常处理
        return false;
    }

    // 如果是图片查看器的按键事件
    if (event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        // 检查是否是CustomDialog类型的对象（图片查看器）
        CustomDialog *dialog = qobject_cast<CustomDialog*>(obj);
        if (dialog && dialog->isModal()) {
            // 如果是Q键，关闭图片查看器
            if (keyEvent->key() == Qt::Key_Q) {
                dialog->close();
                return true; // 事件已处理，不再传递
            }
        }
    }

    // 对于其他对象和事件，使用默认处理
    return QMainWindow::eventFilter(obj, event);
}

void MainWindow::loadPictureFiles(bool clearModel)
{
    // 根据参数决定是否清空模型
    if (clearModel) {
        m_pictureFileModel->clear();

        // 设置 ListView 的模型和属性（只在清空模型时设置）
        ui->listView_viewpictures->setModel(m_pictureFileModel);
        ui->listView_viewpictures->setViewMode(QListView::IconMode);
        ui->listView_viewpictures->setIconSize(QSize(128, 128));
        ui->listView_viewpictures->setGridSize(QSize(160, 190)); // 增加高度以容纳多行文本
        ui->listView_viewpictures->setResizeMode(QListView::Adjust);
        ui->listView_viewpictures->setMovement(QListView::Static);
        ui->listView_viewpictures->setWrapping(true);
        ui->listView_viewpictures->setSpacing(8);
        ui->listView_viewpictures->setWordWrap(true); // 启用文字换行
        // 安装事件过滤器，确保Q键事件能传递到主窗口
        ui->listView_viewpictures->installEventFilter(this);

        // 设置焦点策略，避免蓝色焦点框
        ui->listView_viewpictures->setFocusPolicy(Qt::StrongFocus);

        // 设置 ListView 样式
        ui->listView_viewpictures->setStyleSheet(
            "QListView {"
            "   background-color: rgba(119, 118, 123, 127);"
            "   border: none;"
            "   outline: none;"
            "   show-decoration-selected: 0;"
            "}"
            "QListView::item {"
            "   background-color: rgba(50, 50, 50, 150);"
            "   border: 1px solid rgba(100, 100, 100, 100);"
            "   border-radius: 8px;"
            "   margin: 5px;"
            "   padding: 8px;"
            "   text-align: center;"
            "   outline: none;"
            "}"
            "QListView::item:hover {"
            "   background-color: rgba(70, 70, 70, 180);"
            "   border: 1px solid rgba(150, 150, 150, 150);"
            "   outline: none;"
            "}"
            "QListView::item:selected {"
            "   background-color: rgba(100, 150, 200, 180);"
            "   border: 2px solid rgba(150, 200, 255, 200);"
            "   outline: none;"
            "}"
            "QListView::item:focus {"
            "   outline: none;"
            "   border: 2px solid rgba(150, 200, 255, 200);"
            "}"
        );
    }

    // 扫描图片目录
    QString pictureDir = "/data/photos";
    QDir dir(pictureDir);

    if (!dir.exists()) {
        // 如果目录不存在，只在清空模型时添加提示项
        if (clearModel) {
            QStandardItem* item = new QStandardItem("图片目录不存在或为空");
            item->setFlags(Qt::ItemIsEnabled); // 不可选择
            item->setTextAlignment(Qt::AlignCenter);
            m_pictureFileModel->appendRow(item);
        }
        return;
    }

    // 获取所有图片文件，按修改时间排序（最新的在前）
    QStringList filters;
    filters << "*.jpg" << "*.jpeg" << "*.png" << "*.bmp" << "*.gif";
    QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files, QDir::Time);

    if (fileList.isEmpty()) {
        // 如果没有图片文件，只在清空模型时添加提示项
        if (clearModel) {
            QStandardItem* item = new QStandardItem("暂无图片文件");
            item->setFlags(Qt::ItemIsEnabled); // 不可选择
            item->setTextAlignment(Qt::AlignCenter);
            m_pictureFileModel->appendRow(item);
        }
        return;
    }

    // 如果不清空模型，需要获取现有的文件路径列表，避免重复添加
    QSet<QString> existingFiles;
    if (!clearModel) {
        for (int i = 0; i < m_pictureFileModel->rowCount(); ++i) {
            QStandardItem* item = m_pictureFileModel->item(i);
            if (item) {
                QString filePath = item->data(Qt::UserRole).toString();
                if (!filePath.isEmpty()) {
                    existingFiles.insert(filePath);
                }
            }
        }
    }

    // 为每个图片文件创建列表项
    for (const QFileInfo& fileInfo : fileList) {
        // 如果不清空模型，检查文件是否已存在
        if (!clearModel && existingFiles.contains(fileInfo.absoluteFilePath())) {
            continue; // 跳过已存在的文件
        }

        // 直接使用图片作为图标
        QPixmap pixmap(fileInfo.absoluteFilePath());
        if (!pixmap.isNull()) {
            // 缩放图片到合适的缩略图大小
            QPixmap thumbnail = pixmap.scaled(128, 128, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            QIcon icon(thumbnail);

            // 创建文件名（去掉扩展名）
            QString baseName = fileInfo.completeBaseName();

            // 处理长文件名，智能换行
            QString displayName = baseName;
            if (baseName.length() > 15) {
                // 如果文件名太长，在合适的位置插入换行符
                // 优先在下划线、连字符或数字后换行
                QRegExp breakPoints("[_-]|(?<=\\d)(?=\\D)|(?<=\\D)(?=\\d)");
                QStringList parts;
                int lastIndex = 0;
                int index = 0;

                while ((index = breakPoints.indexIn(baseName, lastIndex)) != -1) {
                    if (index - lastIndex > 8) { // 如果这一段够长了
                        parts.append(baseName.mid(lastIndex, index - lastIndex + 1));
                        lastIndex = index + 1;
                    }
                }

                // 添加剩余部分
                if (lastIndex < baseName.length()) {
                    parts.append(baseName.mid(lastIndex));
                }

                // 如果没有找到合适的断点，按固定长度分割
                if (parts.isEmpty() || parts.size() == 1) {
                    parts.clear();
                    for (int i = 0; i < baseName.length(); i += 12) {
                        parts.append(baseName.mid(i, 12));
                    }
                }

                displayName = parts.join("\n");
            }

            // 创建列表项
            QStandardItem* item = new QStandardItem(icon, displayName);
            item->setData(fileInfo.absoluteFilePath(), Qt::UserRole); // 存储完整路径
            item->setFlags(Qt::ItemIsEnabled | Qt::ItemIsSelectable);
            item->setTextAlignment(Qt::AlignCenter);

            // 如果不清空模型，新图片插入到最前面（最新的在前）
            if (!clearModel) {
                m_pictureFileModel->insertRow(0, item);
            } else {
                m_pictureFileModel->appendRow(item);
            }
        }
    }

        QModelIndex firstIndex = m_pictureFileModel->index(0, 0);
        ui->listView_viewpictures->setCurrentIndex(firstIndex);
        ui->listView_viewpictures->selectionModel()->select(firstIndex, QItemSelectionModel::ClearAndSelect);

        // 确保 ListView 获得焦点，这样键盘导航才能工作
        ui->listView_viewpictures->setFocus();

        qDebug() << "已默认选中第一个图片文件并设置焦点";
}

void MainWindow::viewSelectedPicture()
{
    // 获取当前选中的项
    QModelIndex currentIndex = ui->listView_viewpictures->currentIndex();
    if (!currentIndex.isValid()) {
        qWarning() << "没有选中的图片文件";
        return;
    }

    // 获取文件路径
    QString filePath = currentIndex.data(Qt::UserRole).toString();
    if (filePath.isEmpty()) {
        qWarning() << "无法获取图片文件路径";
        return;
    }

    // 提取文件名（不包含路径）
    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.fileName();

    qDebug() << "准备全屏查看图片文件:" << fileName;

    // 加载图片
    QPixmap pixmap(filePath);
    if (pixmap.isNull()) {
        qWarning() << "无法加载图片文件:" << filePath;
        return;
    }

    // 创建自定义对话框
    CustomDialog *imageDialog = new CustomDialog(this);
    imageDialog->setDialogTitle(fileName); // 设置标题为图片文件名
    imageDialog->hideCloseButton(); // 隐藏关闭按钮

    // 获取屏幕尺寸，计算适合的显示大小（保持宽高比）
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->availableGeometry();
    int maxWidth = screenGeometry.width();  // 最大宽度为屏幕宽度的90%
    int maxHeight = screenGeometry.height(); // 最大高度为屏幕高度的90%

    // 计算缩放后的尺寸（保持宽高比）
    QPixmap scaledPixmap = pixmap.scaled(
        maxWidth, maxHeight,
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
        );

    // 创建 QLabel 显示图片
    QLabel *imageLabel = new QLabel();
    imageLabel->setPixmap(scaledPixmap);
    imageLabel->setAlignment(Qt::AlignCenter);
    imageLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 设置图片标签背景为黑色
    imageLabel->setStyleSheet("QLabel { background-color: black; }");

    // 设置对话框内容
    imageDialog->setDialogContent(imageLabel);

    // 调整窗口大小以适应图片（但不超过屏幕大小）
    imageDialog->setDialogSize(scaledPixmap.width() + 20, scaledPixmap.height() + 60);

    // 安装事件过滤器来处理Q键
    imageDialog->installEventFilter(this);

    // 显示对话框
    imageDialog->exec();

    // 清理
    delete imageDialog;

    // 查看器关闭后，重新激活窗口并获取焦点
    this->activateWindow();
    this->raise();
    this->setFocus();

    // 确保图片列表视图也获得焦点
    if (ui->stackedWidget->currentIndex() == 7) {
        ui->listView_viewpictures->setFocus();
    }

    qDebug() << "图片查看器已关闭，窗口焦点已恢复";
}
